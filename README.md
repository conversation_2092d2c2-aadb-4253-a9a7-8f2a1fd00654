# Parenthing Website : Waitlist

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A modern, responsive web application designed to connect parents with local children's activity providers, including classes, summer camps, tutors, and more. This platform helps parents discover and enroll their children in various educational and recreational activities in their area.

## 🚀 Features

- **Discover Activities**: Browse a wide range of children's activities including classes, summer camps, and workshops
- **Provider Onboarding**: Easy onboarding for activity providers, coaches, and tutors
- **Responsive Design**: Fully responsive layout that works on desktop and mobile devices
- **Modern UI/UX**: Built with a clean, user-friendly interface using modern web technologies
- **Contact Forms**: Easy way for parents to get in touch with activity providers

## 🛠️ Tech Stack

- **Frontend**: 
  - React 18 with TypeScript
  - Vite for fast development and building
  - Tailwind CSS for styling
  - Framer Motion for animations
  - Radix UI components
  - React Router for navigation
  - React Query for data fetching

- **Development Tools**:
  - ESLint for code linting
  - TypeScript for type safety
  - PostCSS for CSS processing

## 🚀 Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/yashthakur1/Parenthing-onbaording-dashboard.git
   cd Parenthing-onbaording-dashboard
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

3. Start the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. Open [http://localhost:5173](http://localhost:5173) in your browser to see the application.

### Building for Production

```bash
npm run build
# or
yarn build
```

## 📂 Project Structure

```
src/
├── components/     # Reusable UI components
├── hooks/         # Custom React hooks
├── lib/           # Utility functions and configurations
├── pages/         # Page components
└── App.tsx        # Main application component
```

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Radix UI](https://www.radix-ui.com/) for accessible UI primitives
- [Tailwind CSS](https://tailwindcss.com/) for utility-first CSS
- [Framer Motion](https://www.framer.com/motion/) for animations
- [React Icons](https://react-icons.github.io/react-icons/) for icons

## 📬 Contact

For any questions or feedback, please open an issue on GitHub or contact the maintainers.
