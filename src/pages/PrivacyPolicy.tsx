import Header from "@/components/Header";
import Footer from "@/components/Footer";
import { useIsMobile } from "@/hooks/use-mobile";

const PrivacyPolicy = () => {
  const isMobile = useIsMobile();
  
  return (
    <div className="min-h-screen bg-transparent">
      <Header />
      <div className={`container mx-auto px-4 ${isMobile ? 'py-[60px]' : 'py-[100px]'}`}>
        <div className="max-w-3xl mx-auto">
          <h1
            style={{ 
              fontSize: isMobile ? "38px" : "58px", 
              fontWeight: 500 
            }}
            className="font-rubik mb-2"
          >
            Privacy Policy
          </h1>
          {/* <p
            style={{ 
              fontSize: isMobile ? "16px" : "20px", 
              fontWeight: 400 
            }}
            className="text-gray-500 mb-6 md:mb-8 font-rubik"
          >
            Effective Date: [Insert Date]
          </p> */}

          <div className="space-y-6 text-gray-700 mt-[100px]">
            <section>
              <p
                className="mb-8  font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                Welcome to Parenthing!
                <br />
                We value your trust and are committed to protecting your
                privacy. This Privacy Policy explains how we collect, use,
                share, and safeguard your information when you use our website (
                <a
                  href="https://www.parenthingapp.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="underline hover:text-indigo-800"
                >
                  www.parenthingapp.com
                </a>
                ) and our mobile application Parenthing ("we", "our", or "us").
                <br />
                By using our services, you agree to the terms of this Privacy
                Policy.
              </p>
            </section>

            <section>
              <h2
                className="font-rubik mb-3"
                style={{
                  fontWeight: 500,
                  fontSize: "32px",
                  lineHeight: "130%",
                  letterSpacing: "0%",
                  color: "#2E2B43",
                }}
              >
                1. What Information We Collect
              </h2>
              <p
                className="mb-4 font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                We collect the following types of information:
              </p>
              <div className="mb-4">
                <p
                  className="mb-2 font-rubik"
                  style={{
                    fontWeight: 600,
                    fontSize: "16px",
                    lineHeight: "150%",
                    letterSpacing: "0%",
                  }}
                >
                  From Parents:
                </p>
                <ul
                  className="list-disc pl-6 space-y-1 font-rubik"
                  style={{
                    fontWeight: 400,
                    fontSize: "16px",
                    lineHeight: "150%",
                    letterSpacing: "0%",
                  }}
                >
                  <li className="leading-tight">Name</li>
                  <li className="leading-tight">Email address</li>
                  <li className="leading-tight">Mobile number</li>
                  <li className="leading-tight">City and locality</li>
                  <li className="leading-tight">
                    Child's name, age, interests
                  </li>
                </ul>
              </div>
              <div className="mb-4">
                <p
                  className="mb-2 font-rubik"
                  style={{
                    fontWeight: 600,
                    fontSize: "16px",
                    lineHeight: "150%",
                    letterSpacing: "0%",
                  }}
                >
                  From Businesses:
                </p>
                <ul
                  className="list-disc pl-6 space-y-1 font-rubik"
                  style={{
                    fontWeight: 400,
                    fontSize: "16px",
                    lineHeight: "150%",
                    letterSpacing: "0%",
                  }}
                >
                  <li className="leading-tight">Business name</li>
                  <li className="leading-tight">Contact person's name</li>
                  <li className="leading-tight">Email address</li>
                  <li className="leading-tight">Phone number</li>
                  <li className="leading-tight">Location</li>
                  <li className="leading-tight">
                    Types of classes or events offered
                  </li>
                </ul>
              </div>
              <div>
                <p
                  className="mb-2 font-rubik"
                  style={{
                    fontWeight: 600,
                    fontSize: "16px",
                    lineHeight: "150%",
                    letterSpacing: "0%",
                  }}
                >
                  Automatically Collected Information (once the app is
                  launched):
                </p>
                <ul
                  className="list-disc pl-6 space-y-1 font-rubik"
                  style={{
                    fontWeight: 400,
                    fontSize: "16px",
                    lineHeight: "150%",
                    letterSpacing: "0%",
                  }}
                >
                  <li className="leading-tight">
                    Device type and operating system
                  </li>
                  <li className="leading-tight">IP address</li>
                  <li className="leading-tight">
                    Usage data and interactions within the app
                  </li>
                  <li className="leading-tight">
                    Location data (with your permission)
                  </li>
                </ul>
              </div>
            </section>

            <section>
              <h2
                className="font-rubik mb-3"
                style={{
                  fontWeight: 500,
                  fontSize: "32px",
                  lineHeight: "130%",
                  letterSpacing: "0%",
                  color: "#2E2B43",
                }}
              >
                2. How We Use Your Information
              </h2>
              <p
                className="mb-1 font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                We use the collected data to:
              </p>
              <ul
                className="list-disc pl-6 space-y-1 font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                <li>
                  Connect parents with relevant events, classes, and businesses
                </li>
                <li>Help businesses reach interested and local families</li>
                <li>Improve our recommendations and user experience</li>
                <li>Send updates, reminders, and personalized suggestions</li>
                <li>Respond to inquiries and provide support</li>
                <li>
                  Communicate promotions, offers, and new features (only if you
                  opt in)
                </li>
              </ul>
            </section>

            <section>
              <h2
                className="font-rubik mb-3"
                style={{
                  fontWeight: 500,
                  fontSize: "32px",
                  lineHeight: "130%",
                  letterSpacing: "0%",
                  color: "#2E2B43",
                }}
              >
                3. How We Share Your Information
              </h2>
              <p
                className="mb-6 font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                We <span className="font-bold">do not sell your data</span>. We
                may share limited information:
              </p>
              <ul
                className="list-disc pl-6 space-y-1 font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                <li>
                  With vendors and service providers involved in running the app
                  (e.g., hosting, email/WhatsApp services)
                </li>
                <li>
                  With event or class organizers when you express interest or
                  book
                </li>
                <li>If required by law or legal processes</li>
                <li>In case of a merger or acquisition, with the new owners</li>
              </ul>
            </section>

            <section>
              <h2
                className="font-rubik mb-3"
                style={{
                  fontWeight: 500,
                  fontSize: "32px",
                  lineHeight: "130%",
                  letterSpacing: "0%",
                  color: "#2E2B43",
                }}
              >
                4. Data Storage & Security
              </h2>
              <p
                className="mb-4 font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                <p className="mb-4">
                  Your data is stored securely on encrypted servers. We take
                  appropriate technical and organizational measures to protect
                  your information from unauthorized access, misuse, or
                  disclosure.
                </p>
                <p>
                  However, no method of transmission over the internet is 100%
                  secure, so we cannot guarantee absolute security.
                </p>
              </p>
            </section>

            <section>
              <h2
                className="font-rubik mb-3"
                style={{
                  fontWeight: 500,
                  fontSize: "32px",
                  lineHeight: "130%",
                  letterSpacing: "0%",
                  color: "#2E2B43",
                }}
              >
                5. Your Rights
              </h2>
              <p
                className=" mb-4 font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                You have the right to:
              </p>
              <ul
                className="list-disc pl-6 space-y-1 font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                <li>Access, correct, or delete your personal information</li>
                <li>Withdraw consent at any time</li>
                <li>Request that we stop sending marketing communications</li>
              </ul>
              <p
                className="mt-4 font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                To exercise any of these rights, please email us at{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="font-bold  hover:text-indigo-800"
                >
                  <EMAIL>
                </a>
                .
              </p>
            </section>

            <section>
              <h2
                style={{
                  fontWeight: 500,
                  fontSize: "32px",
                  lineHeight: "130%",
                  letterSpacing: "0%",
                  color: "#2E2B43",
                }}
                className="font-rubik mb-3"
              >
                6. Children's Privacy
              </h2>
              <p
                className=" font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                We collect child-related data only with the consent of parents
                or guardians and only for the purpose of improving the
                recommendations for your child. We do not allow children under
                13 to create accounts independently.
              </p>
            </section>

            <section>
              <h2
                style={{
                  fontWeight: 500,
                  fontSize: "32px",
                  lineHeight: "130%",
                  letterSpacing: "0%",
                  color: "#2E2B43",
                }}
                className="font-rubik mb-3"
              >
                7. Cookies and Tracking
              </h2>
              <p
                className="mb-4 font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                Our website may use cookies to improve functionality and
                understand how you interact with it. You can manage your cookie
                preferences via your browser settings.
              </p>
            </section>

            <section>
              <h2
                style={{
                  fontWeight: 500,
                  fontSize: "32px",
                  lineHeight: "130%",
                  letterSpacing: "0%",
                  color: "#2E2B43",
                }}
                className="font-rubik mb-3"
              >
                8. Changes to This Policy
              </h2>
              <p
                className=" font-rubik"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                We may update this policy from time to time. We will notify you
                of any significant changes via email or in-app alerts. Please
                review this page periodically to stay informed.
              </p>
            </section>

            <section>
              <h2
                style={{
                  fontWeight: 500,
                  fontSize: "32px",
                  lineHeight: "130%",
                  letterSpacing: "0%",
                  color: "#2E2B43",
                }}
                className="font-rubik mb-3"
              >
                9. Contact Us
              </h2>
              <p
                className=" font-rubik mb-4"
                style={{
                  fontWeight: 400,
                  fontSize: "16px",
                  lineHeight: "150%",
                  letterSpacing: "0%",
                }}
              >
                If you have any questions or concerns about this Privacy Policy
                or your personal data, please contact:
              </p>
              <p>
                <span className="font-bold">Parenthing Team</span>
                {/* <br /> */}
                {/* 📧 <EMAIL> */}
                <br />
               <a  href="https://www.parenthingapp.com" >🌐 www.parenthingapp.com</a>
              </p>
            </section>
          </div>
        </div>
      </div>
      <Footer />
    </div>
  );
};

export default PrivacyPolicy;
