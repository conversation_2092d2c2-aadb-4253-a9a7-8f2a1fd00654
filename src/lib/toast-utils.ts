import { toast } from "@/components/ui/sonner";

// Success toast
export const showSuccess = (message: string) => {
  toast.success(message);
};

// Error toast
export const showError = (message: string) => {
  toast.error(message);
};

// Info toast
export const showInfo = (message: string) => {
  toast.info(message);
};

// Warning toast
export const showWarning = (message: string) => {
  toast.warning(message);
};

// Custom toast with more options
export const showCustomToast = (
  message: string, 
  description?: string,
  options?: {
    duration?: number;
    action?: {
      label: string;
      onClick: () => void;
    };
  }
) => {
  toast(message, {
    description,
    duration: options?.duration,
    action: options?.action ? {
      label: options.action.label,
      onClick: options.action.onClick,
    } : undefined,
  });
};