import { initializeApp } from "firebase/app";
import { getAnalytics, logEvent, setAnalyticsCollectionEnabled } from 'firebase/analytics';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDQga8UJ33gwycYI_xERjWjhV3VO5TJ1ac",
  authDomain: "parenthing-app.firebaseapp.com",
  projectId: "parenthing-app",
  storageBucket: "parenthing-app.firebasestorage.app",
  messagingSenderId: "272995004120",
  appId: "1:272995004120:web:645fb34c5af294151e31ad",
  measurementId: "G-X3V0KPTP1C"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);

// Ensure analytics collection is enabled
setAnalyticsCollectionEnabled(analytics, true);

// Helper function to log events
export const logAnalyticsEvent = (eventName: string, eventParams?: Record<string, any>) => {
  // Only log to console in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`Analytics event: ${eventName}`, eventParams);
  }
  
  try {
    // Make sure we're using the correct event name format
    // Standard event names: https://firebase.google.com/docs/analytics/events?platform=web
    logEvent(analytics, eventName, eventParams);
    
    // Only log success in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`✅ Successfully sent event: ${eventName}`);
    }
  } catch (error) {
    // Always log errors, but with less detail in production
    if (process.env.NODE_ENV === 'development') {
      console.error(`❌ Failed to send event: ${eventName}`, error);
    } else {
      console.error(`Failed to send analytics event`);
    }
  }
};

export { analytics };
