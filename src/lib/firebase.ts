import { initializeApp } from 'firebase/app';
import { getAnalytics, logEvent } from 'firebase/analytics';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCSP2Se7lLMrsw2aNy4cdqHb3P0BPcZljs",
  authDomain: "parenthing-app.firebaseapp.com",
  projectId: "parenthing-app",
  storageBucket: "parenthing-app.firebasestorage.app",
  messagingSenderId: "272995004120",
  appId: "1:272995004120:web:d1f1c64c8ba5c5c61e31ad",
  measurementId: "G-4WP7XZZJZ3"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const analytics = getAnalytics(app);

// Helper function to log events
export const logAnalyticsEvent = (eventName: string, eventParams?: Record<string, any>) => {
  logEvent(analytics, eventName, eventParams);
};

export { analytics };