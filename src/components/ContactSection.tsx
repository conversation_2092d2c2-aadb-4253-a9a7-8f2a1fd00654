
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";
import { Link } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { toast } from "@/components/ui/sonner";
import { logAnalyticsEvent } from "@/lib/firebase";

const ContactSection = () => {
  const isMobile = useIsMobile();
  const [formData, setFormData] = useState({
    name: "",
    mobile: "",
    message: ""
  });
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    
    // Validation for mobile field - only allow numbers and limit to 10 digits
    if (name === "mobile") {
      if (!/^\d*$/.test(value)) {
        return;
      }
      if (value.length > 10) {
        return;
      }
    }
    
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTermsAccepted(e.target.checked);
  };

  const validateForm = () => {
    // Check if all required fields are filled
    if (!formData.name.trim()) {
      toast.error("Please enter your name");
      return false;
    }
    
    if (!formData.mobile.trim()) {
      toast.error("Please enter your WhatsApp number");
      return false;
    }
    
    // Validate mobile number format (exactly 10 digits)
    if (formData.mobile.length !== 10) {
      toast.error("Please enter a valid 10-digit phone number");
      return false;
    }
    
    if (!formData.message.trim()) {
      toast.error("Please enter your message");
      return false;
    }
    
    if (!termsAccepted) {
      toast.error("Please accept the Terms and Conditions to proceed");
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Prepare the payload for query type
      const payload = {
        name: formData.name.trim(),
        mobile: formData.mobile.trim(),
        type: "query",
        message: formData.message.trim()
      };
      
      // Call the API
      const response = await fetch('https://2ak97jqr1e.execute-api.ap-south-1.amazonaws.com/staging/onboarding/joinwaitlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // Track successful contact form submission
        logAnalyticsEvent('contact_form_submit', {
          submit_success: true
        });
        
        toast.success("Your message has been sent successfully!");
        // Reset form
        setFormData({ name: "", mobile: "", message: "" });
        setTermsAccepted(false);
      } else {
        // Track failed contact form submission
        logAnalyticsEvent('contact_form_submit', {
          submit_success: false,
          error_message: data.message || "Something went wrong"
        });
        
        toast.error(data.message || "Something went wrong");
      }
    } catch (error) {
      // Track error in contact form submission
      logAnalyticsEvent('contact_form_submit', {
        submit_success: false,
        error_message: "Network error"
      });
      
      console.error("Error submitting form:", error);
      toast.error("Failed to submit. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const headingStyle = {
    fontFamily: 'Rubik, sans-serif',
    fontWeight: 500,
    fontSize: isMobile ? '36px' : '58px',
    lineHeight: '120%',
    letterSpacing: '0%'
  };

  // Common input styles to remove focus outlines and add font styling
  const inputStyles = "focus:outline-none focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none focus:border-gray-300 font-rubik text-[16px] font-[400] placeholder:text-[#AFAEB6] placeholder:font-rubik placeholder:font-[400] placeholder:text-[16px]";

  // Label styles
  const labelStyle = {
    fontFamily: 'Rubik, sans-serif',
    fontWeight: 400,
    fontSize: '16px',
    lineHeight: '140%',
    letterSpacing: '0%',
    color: '#2E2B43'
  };

  // Button styles
  const buttonStyle = "bg-indigo-600 hover:bg-indigo-700 text-white font-rubik text-[16px] font-[600] leading-[110%] tracking-[0%] px-12 py-3 rounded-full h-[48px] mt-4";

  // Update the checkbox label styling
  const checkboxLabelStyle = {
    fontFamily: 'Rubik, sans-serif',
    fontWeight: 400,
    fontSize: '14px',
    lineHeight: '150%',
    letterSpacing: '0%',
    verticalAlign: 'middle'
  };

  const linkStyle = {
    fontFamily: 'Rubik, sans-serif',
    fontWeight: 400,
    fontSize: '14px',
    lineHeight: '150%',
    letterSpacing: '0%',
    verticalAlign: 'middle',
    textDecoration: 'underline'
  };

  return (
    <section className="py-12 md:py-24 3xl:py-32">
      <div className="w-full flex justify-center px-4 md:px-8 lg:px-12 xl:px-16">
        <div className="w-full max-w-[1440px]">
          <div className="flex flex-col md:flex-row gap-12">
            {/* Left side - Heading */}
            <div className="md:w-1/2 flex flex-col justify-start">
              <h2 style={headingStyle} className="text-gray-900 mb-8">
                <div>Have questions?</div>
                <div>Reach out – we're</div>
                <div>happy to help!</div>
              </h2>
            </div>
            
            {/* Right side - Form */}
            <div className="md:w-1/2">
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label htmlFor="name" style={labelStyle} className="block mb-1">
                    Name
                  </label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    placeholder="e.g John Doe"
                    maxLength={50}
                    className={`w-full h-[64px] ${inputStyles}`}
                  />
                </div>
                
                <div>
                  <label htmlFor="mobile" style={labelStyle} className="block mb-1">
                    WhatsApp number
                  </label>
                  <Input
                    id="mobile"
                    name="mobile"
                    type="tel"
                    value={formData.mobile}
                    onChange={handleChange}
                    placeholder="e.g 1234567890"
                    maxLength={10}
                    className={`w-full h-[64px] ${inputStyles}`}
                  />
                </div>
                
                <div>
                  <label htmlFor="message" style={labelStyle} className="block mb-1">
                    Message
                  </label>
                  <Textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    placeholder="Type your message..."
                    maxLength={500}
                    className={`w-full min-h-[150px] ${inputStyles}`}
                  />
                </div>
                
                <div className="flex items-center">
                  <input 
                    type="checkbox" 
                    id="terms" 
                    checked={termsAccepted}
                    onChange={handleCheckboxChange}
                    className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-0 focus:ring-offset-0"
                  />
                  <label htmlFor="terms" style={checkboxLabelStyle} className="ml-2 block">
                    By submitting, You agree to the <Link to="/terms" style={linkStyle} className="text-indigo-600">Terms and Conditions</Link> and <Link to="/privacy" style={linkStyle} className="text-indigo-600">Privacy policy</Link>
                  </label>
                </div>
                
                <div>
                  <Button 
                    type="submit" 
                    className={buttonStyle}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Submitting..." : "Submit"}
                  </Button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
