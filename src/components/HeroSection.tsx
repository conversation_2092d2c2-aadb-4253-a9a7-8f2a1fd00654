
import { motion, AnimatePresence } from "framer-motion";
import WhatsAppInput from "./WhatsAppInput";
import { useIsMobile } from "@/hooks/use-mobile";
import { useState, useEffect, useRef } from "react";

const HeroSection = () => {
  const isMobile = useIsMobile();
  const [currentTextIndex, setCurrentTextIndex] = useState(0);
  // Set a fixed height instead of dynamic height
  const [textHeight, setTextHeight] = useState(isMobile ? 60 : 80);
  const textRef = useRef<HTMLSpanElement>(null);
  
  const activityTexts = [
    "Classes and Events",
    "Summer Camps",
    "Chess Classes",
    "Storytelling Sessions",
    "Basketball Coach",
    "Swimming Classes",
    "Phonics Tutors",
    "Coding Workshops",
    "Academic Tutors",
    "Dance Classes",
    "Gymnastic coaching",
    "Football Classes",
    "Drawing Workshops",
    "Music teachers",
    "French tuitions",
    "Competitions"
  ];
  
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTextIndex((prevIndex) => 
        (prevIndex + 1) % activityTexts.length
      );
    }, 3000); // Change text every 3 seconds
    
    return () => clearInterval(interval);
  }, []);
  
  // Update this useEffect to set fixed heights based on screen size
  useEffect(() => {
    // Set fixed heights based on screen size
    setTextHeight(isMobile ? 36 : 70);
  }, [isMobile]);
  
  const paragraphStyle = {
    fontFamily: 'Rubik, sans-serif',
    fontSize: isMobile ? '18px' : '24px',
    fontWeight: 300,
    lineHeight: '150%',
    letterSpacing: '0%',
    color: '#626074'  // Secondary color
  };
  
  return (
    <section id="hero-section" className="py-12 md:py-24 3xl:py-32">
      <div className="w-full flex justify-center px-4 md:px-8 lg:px-12 xl:px-16">
        <div className="w-full max-w-[1440px]">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="md:w-1/2 mb-8 md:mb-0">
              <div className="font-rubik font-[500] text-[36px] md:text-[48px] lg:text-[64px] leading-[110%] tracking-[0%] text-gray-900 flex flex-col gap-1">
                <p>Find the best kids'</p>
                
                <div 
                  className="relative w-full overflow-hidden"
                  style={{ height: `${textHeight}px` }}
                >
                  <AnimatePresence mode="wait">
                    <motion.span
                      key={currentTextIndex}
                      ref={textRef}
                      className="text-indigo-600 absolute left-0 right-0 w-full md:text-[48px] lg:text-[64px] font-rubik font-[500] leading-[110%] tracking-[0%]"
                      initial={{ y: 40, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      exit={{ y: -40, opacity: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      {activityTexts[currentTextIndex]}
                    </motion.span>
                  </AnimatePresence>
                </div>
                
                <p className="mb-4">near you</p>
              </div>
              
              <p style={paragraphStyle} className="mb-8 max-w-[35rem] ">
                Get ready to discover fun and interactive learning activities for your child's growth and development – launching soon!
              </p>
              
              <WhatsAppInput 
                buttonText="Join the waitlist" 
                buttonBgColor="bg-indigo-600"
                buttonHoverBgColor="hover:bg-indigo-700"
                buttonTextColor="text-white"
                type="parent"
              />
            </div>
            
            <div className="md:w-1/2 flex justify-center md:justify-end">
              <div className="w-full">
                <img 
                  src="/hero.png" 
                  alt="Parenting App Screenshots" 
                  className="w-full max-w-md md:max-w-[34rem] lg:max-w-[40rem] "
                  style={{
                    WebkitBackfaceVisibility: "hidden",
                    transform: "translateZ(0)",
                    willChange: "transform"
                  }}
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
