
import { useState } from "react";
import { Link } from "react-router-dom";

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  
  const scrollToSection = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
    setIsMenuOpen(false); // Close mobile menu after clicking
  };
  
  // Nav link styles with Rubik font
  const navLinkStyles = "font-rubik text-[16px] font-[300] leading-[140%] tracking-[0%] text-[#2E2B43] hover:text-indigo-600 transition-colors text-center";
  
  return (
    <div className="w-full flex justify-center px-0 md:px-8 lg:px-12 xl:px-16">
      <header className="w-full max-w-[1440px] h-[78px] flex items-center px-4 md:px-8 bg-white md:rounded-full md:shadow-sm md:mb-0">
        <div className="flex items-center justify-between w-full">
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <img className="text-indigo-600 font-bold h-[2.375rem]" src="/parenthing_logo.svg" />
              <h1 className="ml-2 text-[#5E57E1] font-rubik font-[600] text-[24px] leading-[140%] tracking-[0%]">
                Parenthing
              </h1>
            </Link>
          </div>
          
          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-6">
            <Link 
              to="https://medium.com/@parenthingops" 
              target="_blank" 
              rel="noopener noreferrer" 
              className={navLinkStyles}
            >
              Blog
            </Link>
            <button 
              onClick={() => scrollToSection('business-section')} 
              className={navLinkStyles}
            >
              Parenthing for business
            </button>
          </nav>
          
          {/* Mobile menu button */}
          <button 
            className="md:hidden text-[#2E2B43]"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            )}
          </button>
        </div>
        
        {/* Mobile Navigation */}
        <nav 
          className={`md:hidden absolute top-[78px] left-0 right-0 flex flex-col items-center space-y-4 p-4 bg-white border-t border-gray-100 z-10 transition-all duration-300 ease-in-out ${
            isMenuOpen 
              ? "opacity-100 max-h-[200px] translate-y-0" 
              : "opacity-0 max-h-0 -translate-y-2 pointer-events-none"
          } overflow-hidden`}
        >
          <Link 
            to="https://medium.com/@parenthingops" 
            target="_blank" 
            rel="noopener noreferrer" 
            className={navLinkStyles}
          >
            Blog
          </Link>
          <button 
            onClick={() => scrollToSection('business-section')} 
            className={navLinkStyles}
          >
            Parenthing for business
          </button>
        </nav>
      </header>
    </div>
  );
};

export default Header;
