import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { toast } from "@/components/ui/sonner";
import { logAnalyticsEvent } from "@/lib/firebase";

interface WhatsAppInputProps {
  buttonText: string;
  buttonBgColor?: string;
  buttonTextColor?: string;
  buttonHoverBgColor?: string;
  className?: string;
  type?: "parent" | "business";
}

const WhatsAppInput = ({ 
  buttonText, 
  buttonBgColor = "bg-indigo-600", 
  buttonTextColor = "text-white",
  buttonHoverBgColor = "hover:bg-indigo-700",
  className,
  type = "parent"
}: WhatsAppInputProps) => {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [name, setName] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isMobile = useIsMobile();

  const handleButtonClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsModalOpen(true);
    
    // Track waitlist modal open event
    logAnalyticsEvent('waitlist_modal_open', { type });
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Only allow digits and limit to 10 characters
    if (/^\d*$/.test(value) && value.length <= 10) {
      setPhoneNumber(value);
    }
  };

  const validateForm = () => {
    // Check if name is provided
    if (!name.trim()) {
      toast.error("Please enter your name");
      return false;
    }
    
    // Check if phone number is provided
    if (!phoneNumber.trim()) {
      toast.error("Please enter your WhatsApp number");
      return false;
    }
    
    // Validate phone number format (exactly 10 digits)
    if (phoneNumber.length !== 10) {
      toast.error("Please enter a valid 10-digit phone number");
      return false;
    }
    
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Prepare the payload
      const payload = {
        name: name.trim(),
        mobile: phoneNumber.trim(),
        type
      };
      
      // Call the API
      const response = await fetch('https://2ak97jqr1e.execute-api.ap-south-1.amazonaws.com/staging/onboarding/joinwaitlist', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        // Track successful waitlist signup
        logAnalyticsEvent('waitlist_signup', {
          type,
          signup_success: true
        });
        
        // Use the message from the API response instead of a default message
        if (data.data && data.data.message) {
          toast.success(data.data.message);
        } else if (data.msg) {
          toast.success(data.msg);
        } else {
          toast.success("Successfully submitted!");
        }
        
        setIsModalOpen(false);
        setPhoneNumber("");
        setName("");
      } else {
        // Track failed waitlist signup
        logAnalyticsEvent('waitlist_signup', {
          type,
          signup_success: false,
          error_message: data.message || data.msg || "Something went wrong"
        });
        
        toast.error(data.message || data.msg || "Something went wrong");
      }
    } catch (error) {
      // Track error in waitlist signup
      logAnalyticsEvent('waitlist_signup', {
        type,
        signup_success: false,
        error_message: "Network error"
      });
      
      console.error("Error submitting form:", error);
      toast.error("Failed to submit. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const buttonClassName = `rounded-[70px] h-[48px] ${isMobile ? 'w-full' : 'min-w-[150px]'} px-8 font-rubik text-[16px] font-[600] leading-[110%] tracking-[0%] ${buttonBgColor} ${buttonHoverBgColor} ${buttonTextColor} mr-3`;

  // Common button style for both main and modal buttons
  const commonButtonStyle = `rounded-[70px] min-w-[150px] font-rubik text-[16px] font-[600] leading-[110%] tracking-[0%] ${buttonBgColor} ${buttonHoverBgColor} ${buttonTextColor}`;

  // Common input styles to completely remove focus effects but add subtle bg change and style placeholder
  const inputStyles = "caret-text-gray-800 pl-14 focus:outline-none focus:ring-0 focus:ring-offset-0 focus-visible:ring-0 focus-visible:ring-offset-0 focus-visible:outline-none focus:border-gray-200 font-rubik text-[16px] font-[400] leading-[150%] tracking-[0%]";

  // Add this CSS rule for placeholder styling
  const placeholderStyles = {
    "::placeholder": {
      color: "#AFAEB6",
      fontFamily: "Rubik, sans-serif",
      fontWeight: 400,
      fontSize: "16px",
      lineHeight: "150%",
      letterSpacing: "0%",
      opacity: 1, /* Firefox */
    }
  };

  // Mobile layout
  if (isMobile) {
    return (
      <>
        <div className={cn("flex flex-col gap-4 max-w-md", className)}>
          <div className={`relative w-full ${isFocused ? 'bg-gray-50' : 'bg-white'} rounded-full transition-colors duration-200`}>
            <div className="absolute left-5 top-1/2 transform -translate-y-1/2 z-10">
              <img src="/whatsappIcon.svg" alt="WhatsApp Logo" className="w-6 h-6" />
            </div>
            <Input
              type="tel"
              placeholder="WhatsApp number"
              value={phoneNumber}
              onChange={handlePhoneChange}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
              maxLength={10}
              className={`${inputStyles} pr-3 py-2 h-[48px] w-full rounded-full border border-gray-200 bg-transparent shadow-sm`}
              style={placeholderStyles}
            />
          </div>
          <Button 
            onClick={handleButtonClick}
            type="button" 
            className={buttonClassName}
          >
            {buttonText}
          </Button>
        </div>

        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Complete your information</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4 py-4">
              <div className="space-y-2">
                <label htmlFor="modal-phone" className="text-sm font-medium">WhatsApp Number</label>
                <Input
                  id="modal-phone"
                  type="tel"
                  value={phoneNumber}
                  onChange={handlePhoneChange}
                  maxLength={10}
                  className="w-full"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="modal-name" className="text-sm font-medium">Your Name</label>
                <Input
                  id="modal-name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter your name"
                  maxLength={50}
                  className="w-full"
                />
              </div>
              <DialogFooter>
                <Button 
                  type="submit" 
                  className={commonButtonStyle}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit"}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </>
    );
  }

  // Desktop layout
  return (
    <>
      <div className={cn(`flex items-center max-w-md ${isFocused ? 'bg-gray-50' : 'bg-white'} rounded-full shadow-sm transition-colors duration-200`, className)}>
        <div className="relative flex-grow">
          <div className="absolute left-5 top-1/2 transform -translate-y-1/2 z-10">
            <img src="/whatsappIcon.svg" alt="WhatsApp Logo" className="w-6 h-6" />
          </div>
          <Input
            type="tel"
            placeholder="WhatsApp number"
            value={phoneNumber}
            onChange={handlePhoneChange}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            maxLength={10}
            className={`${inputStyles} pr-3 py-6 h-[64px] border-0 bg-transparent`}
            style={placeholderStyles}
          />
        </div>
        <Button 
          onClick={handleButtonClick}
          type="button" 
          className={buttonClassName}
        >
          {buttonText}
        </Button>
      </div>

      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Complete your information</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="modal-phone" className="text-sm font-medium">WhatsApp Number</label>
              <Input
                id="modal-phone"
                type="tel"
                value={phoneNumber}
                onChange={handlePhoneChange}
                maxLength={10}
                className="w-full"
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="modal-name" className="text-sm font-medium">Your Name</label>
              <Input
                id="modal-name"
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                placeholder="Enter your name"
                maxLength={50}
                className="w-full"
              />
            </div>
            <DialogFooter>
              <Button 
                type="submit" 
                className={commonButtonStyle}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : "Submit"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </>
  );
};

// Update the default props to use the new color
WhatsAppInput.defaultProps = {
  buttonText: "Join the waitlist",
  buttonBgColor: "bg-indigo-600",
  buttonHoverBgColor: "hover:bg-indigo-700",
  buttonTextColor: "text-white",
  type: "parent"
};

export default WhatsAppInput;
