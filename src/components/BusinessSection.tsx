
import WhatsAppInput from "@/components/WhatsAppInput";
import { motion } from "framer-motion";
import { useIsMobile } from "@/hooks/use-mobile";

const BusinessSection = () => {
  const isMobile = useIsMobile();
  
  const titleStyle = {
    fontFamily: 'Rubik, sans-serif',
    fontWeight: 500,
    fontSize: isMobile ? '36px' : '58px',
    lineHeight: '120%',
    letterSpacing: '0%',
    color: '#2E2B43'  // Primary color
  };
  
  const paragraphStyle = {
    fontFamily: 'Rubik, sans-serif',
    fontSize: isMobile ? '18px' : '20px',
    fontWeight: 300,
    lineHeight: '150%',
    letterSpacing: '0%',
    color: '#626074'  // Secondary color
  };
  
  return (
    <section id="business-section" className="py-12 md:py-24 3xl:py-32">
      <div className="w-full flex justify-center px-4 md:px-8 lg:px-12 xl:px-16">
        <div className="w-full max-w-[1440px]">
          <div className="flex flex-col-reverse md:flex-row items-center justify-between gap-8">
            <div className="md:w-1/2 w-full flex justify-center md:justify-start">
              {/* Wrapper div with full width */}
              <div className="w-full">
                {/* Main image with left alignment */}
                <img 
                  src="/business.png" 
                  alt="Business Owner with Children" 
                  className="w-full max-w-[38rem] mx-auto md:mx-0 rounded-lg" 
                />
              </div>
            </div>
            
            <div className="md:w-1/2">
              <h1 style={titleStyle} className="mb-4">
                Run a business that<br />
                caters to kids?
              </h1>
              <p style={paragraphStyle} className="mb-8 max-w-full">
                If you are a coach, institute, or event organizer that caters to the developmental or academic needs of children, we'd love to have you onboard. Launching soon!
              </p>
              <WhatsAppInput 
                buttonText="Notify me" 
                buttonBgColor="bg-yellow-400"
                buttonHoverBgColor="hover:bg-yellow-500"
                buttonTextColor="text-gray-900"
                type="business"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BusinessSection;
