import { Link } from "react-router-dom";
import { logAnalyticsEvent } from "@/lib/firebase";

// Create a tracking wrapper for social links
const SocialLink = ({ href, platform, children }) => {
  const handleClick = () => {
    logAnalyticsEvent('social_link_click', { platform });
  };
  
  return (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="text-white hover:text-indigo-200 flex items-center justify-center w-8 h-8"
      onClick={handleClick}
    >
      {children}
    </a>
  );
};

const Footer = () => {
  return (
    <footer className="bg-indigo-600 text-white py-12 md:py-16 3xl:py-24">
      <div className="w-full flex justify-center px-4 md:px-8 lg:px-12 xl:px-16">
        <div className="w-full max-w-[1440px]">
          {/* Desktop layout */}
          <div className="hidden md:flex md:flex-row md:justify-between md:items-center md:pb-10 md:mb-8">
            {/* Logo - Left aligned with fixed width */}
            <div className="flex flex-col justify-space-between gap-[24px] w-1/4">
              <div className="flex items-center">
                <img
                  className="h-[2.375rem]"
                  src="/white_parenthing_logo.svg"
                  alt="Parenthing"
                />
                <h1 className="md:block ml-2 text-[#ffffff] font-rubik font-[500] text-[24px] leading-[140%] tracking-[0%]">
                  Parenthing
                </h1>
              </div>
              <p className="font-rubik font-[400] text-[14px] leading-[150%] tracking-[0%]">
                Flat No.: B-602, Cosmos Nest,
                <br />
                Kolshet Road, Off Ghodbunder Road,
                <br />
                Thane, Maharashtra PIN Code: 400607
              </p>
            </div>

            {/* Navigation Links - Center aligned */}
            <div
              className="flex flex-col gap-[32px]  justify-start  font-rubik"
              style={{
                fontSize: "16px",
                fontWeight: 500,
                lineHeight: "150%",
                letterSpacing: "0%",
              }}
            >
              <Link
                to="https://medium.com/@parenthingops"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-indigo-200"
              >
                Blog
              </Link>
              <Link to="/privacy" className="text-white hover:text-indigo-200">
                Privacy policy
              </Link>
              <Link to="/terms" className="text-white hover:text-indigo-200">
                Terms & Conditions
              </Link>
            </div>

            {/* Social Media Icons - Fixed width on desktop */}
            {/* <div className="flex space-x-6 md:space-x-4 mb-12 md:mb-0 md:w-1/4 md:justify-end">
              <a
                href="https://www.facebook.com/parenthing"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-indigo-200 flex items-center justify-center w-8 h-8"
              >
                <img 
                  src="/facebook.svg" 
                  alt="Facebook" 
                  className="h-5 w-5"
                />
              </a>
              <a
                href="https://www.instagram.com/theparenthingapp/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-indigo-200 flex items-center justify-center w-8 h-8"
              >
                <img 
                  src="/instagram.svg" 
                  alt="Instagram" 
                  className="h-5 w-5"
                />
              </a>
              <a
                href="https://x.com/parenthingapp"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-indigo-200 flex items-center justify-center w-8 h-8"
              >
                <img 
                  src="/twitterIcon.svg" 
                  alt="Twitter" 
                  className="h-5 w-5"
                />
              </a>
              <a
                href="https://www.linkedin.com/company/parenthingapp/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-indigo-200 flex items-center justify-center w-8 h-8"
              >
                <img 
                  src="/linkedin.svg" 
                  alt="LinkedIn" 
                  className="h-5 w-5"
                />
              </a>
            </div> */}
          </div>

          {/* Mobile layout */}
          <div className="flex flex-col items-center md:hidden">
            {/* Logo */}
            <div className="flex items-center mb-6">
              <img
                className="h-[2.375rem]"
                src="/white_parenthing_logo.svg"
                alt="Parenthing"
              />
              <h1 className="md:block ml-2 text-[#ffffff] font-rubik font-[500] text-[24px] leading-[140%] tracking-[0%]">
                Parenthing
              </h1>
            </div>

            {/* Address */}
            <p className="font-rubik font-[400] text-[14px] leading-[150%] tracking-[0%] text-center mb-10">
              Flat No.: B-602, Cosmos Nest,
              <br />
              Kolshet Road, Off Ghodbunder Road,
              <br />
              Thane, Maharashtra PIN Code: 400607
            </p>

            {/* Navigation Links */}
            <div
              className="flex flex-col items-center space-y-6 mb-8 font-rubik"
              style={{
                fontSize: "16px",
                fontWeight: 500,
                lineHeight: "150%",
                letterSpacing: "0%",
              }}
            >
              <Link
                to="https://medium.com/@parenthingops"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-indigo-200"
              >
                Blog
              </Link>
              <Link to="/privacy" className="text-white hover:text-indigo-200">
                Privacy policy
              </Link>
              <Link to="/terms" className="text-white hover:text-indigo-200">
                Terms & Conditions
              </Link>
            </div>

            {/* Mobile layout social icons */}
            {/* <div className="flex items-center justify-center space-x-6 mb-4 md:hidden">
              <a
                href="https://www.facebook.com/parenthing"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-indigo-200 flex items-center justify-center w-8 h-8"
              >
                <img src="/facebook.svg" alt="Facebook" className="h-5 w-5" />
              </a>
              <a
                href="https://www.instagram.com/theparenthingapp/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-indigo-200 flex items-center justify-center w-8 h-8"
              >
                <img src="/instagram.svg" alt="Instagram" className="h-5 w-5" />
              </a>
              <a
                href="https://x.com/parenthingapp"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-indigo-200 flex items-center justify-center w-8 h-8"
              >
                <img src="/twitterIcon.svg" alt="Twitter" className="h-5 w-5" />
              </a>
              <a
                href="https://www.linkedin.com/company/parenthingapp/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-indigo-200 flex items-center justify-center w-8 h-8"
              >
                <img src="/linkedin.svg" alt="LinkedIn" className="h-5 w-5" />
              </a>
            </div> */}
          </div>

          {/* <div className="w-full border-t border-opacity-15 border-white mb-8 md:hidden"></div> */}

          {/* Separator Line - Only visible on desktop */}
          <div className="md:block w-full border-t border-opacity-15 border-white mb-8 mt-8"></div>

          {/* Copyright - Column on mobile, row on desktop */}
          <div className="flex flex-col md:flex-row justify-between items-center text-center mb-[40px] md:mb-0 gap-6 md:gap-0">
            <div className="flex space-x-4 md:space-x-4 mb-4 md:mb-0 order-1 md:order-none">
              <SocialLink href="https://www.facebook.com/parenthing" platform="facebook">
                <img src="/facebook.svg" alt="Facebook" className="h-5 w-5" />
              </SocialLink>
              <SocialLink href="https://www.instagram.com/theparenthingapp/" platform="instagram">
                <img src="/instagram.svg" alt="Instagram" className="h-5 w-5" />
              </SocialLink>
              <SocialLink href="https://x.com/parenthingapp" platform="twitter">
                <img src="/twitterIcon.svg" alt="Twitter" className="h-5 w-5" />
              </SocialLink>
              <SocialLink href="https://www.linkedin.com/company/parenthingapp/" platform="linkedin">
                <img src="/linkedin.svg" alt="LinkedIn" className="h-5 w-5" />
              </SocialLink>
            </div>
            <p
              className="font-rubik order-2 md:order-none"
              style={{
                fontSize: "14px",
                fontWeight: 400,
                lineHeight: "150%",
                letterSpacing: "0%",
              }}
            >
              © {new Date().getFullYear()} ParentEase EdTech Private Limited.
              All rights reserved.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
