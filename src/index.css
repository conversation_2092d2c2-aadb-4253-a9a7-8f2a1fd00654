
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 246 68% 61%; /* Updated to match #5E57E1 */
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 246 68% 61%; /* Updated to match #5E57E1 */

    --radius: 0.75rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  html {
    font-family: 'Inter', sans-serif;
    -webkit-text-size-adjust: 100%; /* Prevent iOS text size adjust */
  }

  body {
    @apply bg-background text-foreground;
    -webkit-tap-highlight-color: transparent;
  }
}

/* Add this utility class for preventing text selection */
.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Custom Classes */
.btn-primary {
  @apply bg-indigo-600 hover:bg-indigo-700 text-white font-medium px-8 py-3 rounded-full transition-colors duration-300;
}

.btn-secondary {
  @apply bg-yellow-400 hover:bg-yellow-500 text-gray-900 font-medium px-8 py-3 rounded-full transition-colors duration-300;
}

.section-padding {
  @apply py-12 md:py-20 px-4 md:px-8;
}

/* Add placeholder styling */
::placeholder {
  color: #AFAEB6 !important;
  font-family: 'Rubik', sans-serif !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  line-height: 150% !important;
  letter-spacing: 0% !important;
  opacity: 1 !important; /* Firefox */
}

::-webkit-input-placeholder { /* Chrome/Opera/Safari */
  color: #AFAEB6 !important;
  font-family: 'Rubik', sans-serif !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  line-height: 150% !important;
  letter-spacing: 0% !important;
}

::-moz-placeholder { /* Firefox 19+ */
  color: #AFAEB6 !important;
  font-family: 'Rubik', sans-serif !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  line-height: 150% !important;
  letter-spacing: 0% !important;
}

:-ms-input-placeholder { /* IE 10+ */
  color: #AFAEB6 !important;
  font-family: 'Rubik', sans-serif !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  line-height: 150% !important;
  letter-spacing: 0% !important;
}

/* Improve image rendering on iOS */
img {
  -webkit-backface-visibility: hidden;
  transform: translateZ(0);
}
